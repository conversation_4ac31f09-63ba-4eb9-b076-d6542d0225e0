UNIT TESTING PROCESS FOR DATA MANAGEMENT CODEBASE
==================================================

This document provides a comprehensive guide on how unit testing is implemented and executed 
in the data management codebase, including build configuration, test execution, coverage 
analysis, and reporting.

TABLE OF CONTENTS
=================
1. Overview
2. Build Configuration Setup
3. Codebase Building Process
4. Unit Test Building and Execution
5. buildandrununittest.sh Script Analysis
6. Coverage Report Generation
7. Test Results Processing
8. SonarQube Integration
9. Web Dashboard Generation

1. OVERVIEW
===========
The data management codebase uses Google Test (GTest) framework for unit testing with 
Bullseye Coverage for code coverage analysis. The testing process is automated through 
shell scripts that handle building, execution, and reporting.

Key Components:
- Google Test (GTest) framework for unit testing
- Bullseye Coverage for code coverage analysis
- Imake build system for compilation
- XML output for CI/CD integration
- HTML dashboards for result visualization
- SonarQube integration for quality analysis

2. BUILD CONFIGURATION SETUP
============================

2.1 Package Dependencies (import.yml)
-------------------------------------
Before building the codebase, ensure the following packages are configured in the 
import.yml file:

```yaml
3p-package:
  GTest:
    version: 'use_global'
    ext: 'tar.gz'
    
  BullseyeCoverage:
    version: 'use_global'
    ext: 'tar.gz'
```

These entries ensure that:
- GTest framework is downloaded from the artifactory
- Bullseye Coverage tools are available for code coverage analysis
- Both packages use the global version specified in the artifactory

2.2 Package Download Process
---------------------------
When the build system processes import.yml:
1. Packages are downloaded from the configured artifactory
2. GTest is extracted to import/GTest/ directory
3. BullseyeCoverage is extracted to import/BullseyeCoverage/ directory
4. Environment variables are set to point to these locations

3. CODEBASE BUILDING PROCESS
============================

3.1 Standard Build (Without Unit Tests)
---------------------------------------
The codebase is built without unit tests by default. This is controlled through 
Imakefile configurations where unit test subdirectories are excluded.

Example Imakefile structure:
```makefile
#define IHaveSubdirs
#define PassCDebugFlags

# Main subdirectories (unit test directories excluded by default)
SUBDIRS = common RAL MicroService DM Tools config Reports DBCleanup

MakeSubdirs($(SUBDIRS))
DependSubdirs($(SUBDIRS))
```

3.2 Unit Test Directory Exclusion
---------------------------------
Unit test directories are excluded from the main build by:
- Not including unit_tests subdirectories in SUBDIRS
- Using conditional compilation flags
- Separate build targets for unit tests

To exclude unit tests completely, remove references like:
- test/unit
- unit_tests
- unittest
From the parent directory Imakefiles.

4. UNIT TEST BUILDING AND EXECUTION
===================================

4.1 Wrapper Script Execution
----------------------------
Unit tests are initiated using the wrapper script RunUnittest.sh from the admin directory:

```bash
cd admin
./RunUnittest.sh
```

This wrapper script:
1. Sets up the overall testing environment
2. Validates prerequisites and dependencies
3. Calls the main buildandrununittest.sh script
4. Handles high-level error reporting and logging

4.2 Main Testing Script
----------------------
The wrapper script then executes the main buildandrununittest.sh script:

```bash
./buildandrununittest.sh
```

This main script:
1. Sets up the build environment
2. Configures Bullseye Coverage (if enabled)
3. Builds individual unit test components
4. Executes all unit tests
5. Generates coverage reports

4.3 Script Hierarchy
-------------------
The testing process follows this script hierarchy:
```
admin/
├── RunUnittest.sh           # Wrapper script (entry point)
└── ...

dm-platform/src/DM/
├── buildandrununittest.sh   # Main testing script
└── ...
```

4.4 Unit Test Structure
----------------------
Each component has its own unit test directory structure:
```
src/DM/ComponentName/
├── src/               # Source code
├── unit_tests/        # Unit test directory
│   ├── Imakefile     # Build configuration
│   ├── TestFile.cpp  # Test implementation
│   └── release/      # Built test executables
```

5. SCRIPT ANALYSIS
==================

5.1 RunUnittest.sh Wrapper Script
---------------------------------
The RunUnittest.sh wrapper script (located in admin directory) serves as the entry point:

Functions:
- Validates the testing environment setup
- Sets up high-level logging and error handling
- Manages overall test execution workflow
- Calls the main buildandrununittest.sh script
- Provides summary reporting and cleanup

5.2 buildandrununittest.sh Main Script
--------------------------------------
The buildandrununittest.sh script is the main automation script that handles the
complete unit testing workflow. It performs the following major functions:

5.3 Environment Setup Phase
---------------------------
```bash
# Set base directories
setenv DM_BASE /path/to/data-management
setenv DM_ROOT ${DM_BASE}/dm-platform/src

# Initialize Bullseye Coverage
if ( -x "${DM_BASE}/import/BullseyeCoverage/bin/cov01" ) then
    echo "Initializing Bullseye Coverage..."
    setenv COVFILE ${DM_BASE}/data-management.cov
    setenv BULLSEYE_ROOT ${DM_BASE}/import/BullseyeCoverage
    setenv PATH ${BULLSEYE_ROOT}/bin:${PATH}
    
    # Set up compiler environment variables for coverage
    setenv CC ${BULLSEYE_ROOT}/bin/gcc
    setenv CXX ${BULLSEYE_ROOT}/bin/g++
    setenv LD ${BULLSEYE_ROOT}/bin/g++
    setenv CPLUSPLUS ${BULLSEYE_ROOT}/bin/g++
    setenv COVERAGE_ENABLED 1
endif
```

5.4 Build Phase
---------------
The script builds unit tests for each component:
```bash
# Build IODCreator Unit Tests
cd $DM_ROOT/src/DM/IODCreator/unit_tests
imboot
make _release CC=${CC} CXX=${CXX} LD=${LD}

# Build IODInstaller Unit Tests  
cd $DM_ROOT/src/DM/IODInstaller/unit_tests
imboot
make _release CC=${CC} CXX=${CXX} LD=${LD}

# Similar process for other components...
```

5.5 Test Execution Phase
------------------------
Each unit test is executed with XML output:
```bash
echo "Running IODCreatorUnitTests"
$DM_ROOT/src/DM/IODCreator/unit_tests/release/IODCreatorTest.lnx \
    "--gtest_output=xml:${DM_BASE}/../unit_tests/GTestReport5.xml"

echo "Running IODInstallerUnitTests"
$DM_ROOT/src/DM/IODInstaller/unit_tests/release/IODInstallerTest.lnx \
    "--gtest_output=xml:${DM_BASE}/../unit_tests/GTestReport6.xml"
```

5.6 Coverage Finalization Phase
-------------------------------
```bash
if ( $COVERAGE_ENABLED == 1 ) then
    echo "Finalizing Bullseye Coverage..."
    ${BULLSEYE_ROOT}/bin/cov01 -0
    
    # Generate HTML coverage report
    ${BULLSEYE_ROOT}/bin/covhtml -f "$COVFILE" \
        ${DM_BASE}/build/coverage/data-management.html
    
    # Generate XML coverage report
    ${BULLSEYE_ROOT}/bin/covxml -f "$COVFILE" \
        -o ${DM_BASE}/build/coverage/data-management.xml
endif
```

6. COVERAGE REPORT GENERATION
=============================

6.1 Coverage File (.cov)
------------------------
- Location: ${DM_BASE}/data-management.cov
- Format: Binary coverage data file
- Contains: Execution counts for each line of code
- Generated by: Bullseye Coverage during test execution

6.2 HTML Coverage Report
------------------------
- Location: ${DM_BASE}/build/coverage/data-management.html
- Format: Interactive HTML report
- Features: 
  * Line-by-line coverage visualization
  * Function and file coverage statistics
  * Drill-down navigation
  * Color-coded coverage indicators

6.3 XML Coverage Report
----------------------
- Location: ${DM_BASE}/build/coverage/data-management.xml
- Format: Structured XML for tool integration
- Used by: SonarQube for coverage analysis
- Contains: Coverage percentages and detailed metrics

7. TEST RESULTS PROCESSING
==========================

7.1 XML Test Results
-------------------
Unit test results are stored in XML format in the unit_tests directory:
- GTestReport5.xml (IODCreator tests)
- GTestReport6.xml (IODInstaller tests)  
- GTestReport7.xml (XDMConfig tests)
- Additional reports for other components

7.2 Test Result Structure
-------------------------
Each XML file contains:
```xml
<testsuites>
  <testsuite name="ComponentTest" tests="10" failures="0" errors="0" time="0.5">
    <testcase name="TestMethod1" status="run" time="0.1"/>
    <testcase name="TestMethod2" status="run" time="0.2"/>
  </testsuite>
</testsuites>
```

7.3 Web Dashboard Generation
----------------------------
The generate_test_dashboard.py script processes XML files to create a comprehensive 
HTML dashboard:

```python
def parse_test_results(xml_file):
    # Parse GTest XML format
    # Extract test counts, failures, timing
    # Generate summary statistics
    
def collect_all_results():
    # Scan unit_tests directory for XML files
    # Process each file
    # Aggregate results
```

8. SONARQUBE INTEGRATION
=======================

8.1 SonarQube Configuration (sonar.properties)
----------------------------------------------
```properties
sonar.projectKey = data-management
sonar.sourceEncoding = UTF-8
sonar.language = cpp
sonar.sources = dm-platform/src,dm-submodules/terra/
sonar.cfamily.bullseye.reportPath = build/coverage/data-management.xml
```

8.2 Coverage %tegration Process
-------------------------------
1. Bullseye generates XML coverage report
2. SonarQube reads the XML file specified in sonar.cfamily.bullseye.reportPath
3. Coverage data is imported into SonarQube analysis
4. Quality gates and coverage thresholds are evaluated
5. Results are displayed in SonarQube dashboard

8.3 Quality Analysis
--------------------
SonarQube provides:
- Code coverage percentages
- Coverage trends over time
- Uncovered code identification
- Quality gate pass/fail status
- Integration with CI/CD pipelines

9. SUMMARY OF COMPLETE WORKFLOW
===============================

1. Configure import.yml with GTest and BullseyeCoverage packages
2. Build main codebase (unit tests excluded by default)
3. Execute wrapper script from admin directory: ./RunUnittest.sh
4. Wrapper script calls buildandrununittest.sh which:
   - Sets up Bullseye Coverage environment
   - Builds all unit test components
   - Executes tests with XML output
   - Generates .cov, HTML, and XML coverage reports
5. XML test results are parsed into web dashboard
6. XML coverage report is consumed by SonarQube
7. Quality analysis and reporting completed

