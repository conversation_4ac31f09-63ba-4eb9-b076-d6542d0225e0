/* Copyright (c) 2020 General Electric Medical Systems, All rights reserved. */
/*****************************************************************************
 FILE
 AppCallbackReader.cpp

 DESCRIPTION
 Reader for SR Dose to be used by RDSR Service

 REV    DATE         BY              SPR & REMARKS
 ----  -----------  ---------------  ------------------------------------------
 1     <USER>   <GROUP>        N/A           Initial Implementation
 2     20-Dec-2017   vishnu	     	  HCSDM00491932 Changes done for getting RDSR Series Instance UID and SOP Instance UID for MPPS N-Set
 3	   08-Jan-2018   Mustahsan		  N/A           Incorporate all images details in SR Document
 4	   06-Mar-2018   Lin<PERSON>/Nithish 	  HCSDM00501328 Coveriy Fix for DM
 5     01-Apr-2018   vishnu           HCSDM00505730 RDSR: Physical DAP disconnection "DAP Communicationerror" does not block Dose SR creation for Digital Exams
 6	   28-May-2018	 Mustahsan/vishnu HCSDM00512618	Added cloneto function (fix for SR image validation)
 7	  	09-Aug-2018  Biju Balan 	HCSDM00501328     Incorrect DAPvalue during exam after DAP-meter is disconnected/DAP Unit change and append exam scenario
 8	   	02-Aug-2018	 Sreeja 			N/A	coverity Issues
 9	   	03-09-2018	 Sreeja	 	  Private tag to enable/disable UI has set during study recreation.
 10		13-May-2019	Ravi        HCSDM00554656    Could not open SR which has only Cassette exposure in MicroDicom Viewer
 11		02-May-2019	Mustahsan	HCSDM00552208	RSDR for TOMO
 12		01-Jul-2019	Mustahsan	HCSDM00554530	Logger levels reassigned 
 13     07-Aug-2019	Prakash B	HCSDM00563501 	Manage RDSR creation in a separate thread
 14     03-Sep-2019	Mustahsan	HCSDM00568329	RDSR gets generated even though DAP meter is not connect and Dose value is null
 15     23-Jan-2020	Prakash B	HCSDM00591786 RDSR Anonymizing Intuition Details
 16	    11-Dec-2020 Mustahsan		NA	CHOP - Measured Values to read and set Code Sequence 
 *****************************************************************************/

#include "AppCallbackReader.h"

#include "DcmDataItem.h"
#include "DcmValue.h"
#include "DcmTagKey.h"
#include "DcmElement.h"

#include "DcmSQElement.h"

#include "RDSRDBReader.h"
#include "SRContentRepo.h"
#include "RDSRCreateParam.h"

#include "DMUtility.h"

#include "SRCompositeItem.h"
#include "SRConceptCodeItem.h"
#include "SRContentItem.h"
#include "SRDateItem.h"
#include "SRDateTimeItem.h"
#include "SRImageRefItem.h"
#include "SRNumItem.h"
#include "SRPNameItem.h"
#include "SRTextItem.h"
#include "SRTimeItem.h"
#include "SRUIDRefItem.h"
#include "SRWaveformRefItem.h"
#include "InMemStream.h"

#include "dmf/DMFItem.h"
#include "dmfdicom/DMFDicomFileItemTemplate.h"
#include "dmf/DMFRepository.h"
#include "dmf/DMFSession.h"

#include "content/ContentTagsPatientModule.h"
#include "content/ContentTagsStudyModule.h"
#include "content/ContentTagsSeriesModule.h"
#include "content/ContentTagsFoRModule.h"
#include "content/ContentTagsEquipmentModule.h"
#include "content/ContentTagsDocumentModule.h"
#include "content/ContentTID10001.h"

#include "Logger.h"
#include "rdsrdefs.h"
#include <sstream>

using namespace DcmDom;
using namespace DcmDom::SRDom;

namespace DoseAppClient
{

AppCallbackReader::AppCallbackReader() : mDRepo(new DcmDom::DcmDataItem),
					 mRemoveDRepo(new DcmDom::DcmDataItem),
					 mCRepo(new SRContentRepo),
					 mSystemType(NoSystemType),
					 mStudyType(NoStudyType),
					 mRDSRParam(0)
{
}

AppCallbackReader::~AppCallbackReader()
{

	if (mDRepo)
		delete mDRepo;
	//Coverity Fix for DM CID 300906
	if (mRemoveDRepo)
	{
		delete mRemoveDRepo;
	}

	if (mCRepo)
		delete mCRepo;
	if (mRDSRParam)
		delete mRDSRParam;
}

// Starting point for the service
// calls DB to get al related details and then fills in local repo

void AppCallbackReader::PopulateDataFor(InMemStream &memStream, DcmDom::SRDom::SRCodeCardXPathsType &xpathsToReplicate,
					int &IrradiationEventCout, std::string &SeriesInstUID, std::string &SOPInstUID,
					DcmDom::SRDom::SRCodeCardXPathsType &xpathsToRemove)
{
	LOG_INFO("Starting RDSR Fill");

	// de-serialize to get original data
	mRDSRParam = new RDSRCreateParam;

	mRDSRParam->OnDeSerialize(memStream);

	//for Debug
	//mRDSRParam->Print(std::cout,0);

	mStudyUID = mRDSRParam->GetStudyUID();
	std::string seriesUID = mRDSRParam->GetSeriesUID();
	std::string sopInstanceUID = mRDSRParam->GetSOPInstanceUID();
	std::string examType = mRDSRParam->GetExamType();
	int oldInstanceNo = mRDSRParam->GetLastInstanceNo();
	std::string csd = mRDSRParam->GetCsdType();

	SetCsdType(csd);

	//first get everything from DB
	RDSRDBReader *dbReader = new RDSRDBReader();

	
	try 
	{
		dbReader->ReadStudyData(mStudyUID);
	} 
	catch (std::runtime_error ex) 
	{
		delete dbReader; //coverity fix for CID :294858
		throw;
	}

	// generate IDs and Time Stamps
	if (!generateData(seriesUID, sopInstanceUID, oldInstanceNo, dbReader))
	{
		LOG_DEBUG("Could not generate static data for the study");
		delete dbReader; //coverity fix for CID :294858
		std::string errorMsg("Could not generate data for the study ");
		throw runtime_error(errorMsg);
	}

	// determine the system , if integrated , cassette or mixed
	if (!DetermineSystemType(dbReader))
	{
		LOG_DEBUG("Could not determine study type for the exposures");
		delete dbReader; //coverity fix for CID :294858
		std::string errorMsg("Could not determine study type for the exposures");
		throw runtime_error(errorMsg);
	}
	LOG_INFO(" The Model is (MOBILE=1, FIXED=2) " << mSystemType << " and study Type is (NoStudyType, WallStand=1, Table, DigitalCassette, Cassette, InHousing, Digital, Mixed ) " << mStudyType);

	// DB call was success / now fill the elments
	RDSR_Status status = Filldata(xpathsToReplicate, xpathsToRemove, dbReader, examType);
	if (status == RDSR_FAILURE)
	{
		LOG_ERROR("Could not transform data");
		delete dbReader; //coverity fix for CID :294858
		std::string errorMsg("Could not transform data");
		throw runtime_error(errorMsg);
	}

	// setting values to retun back
	SeriesInstUID = mSeriesUID;
	SOPInstUID = mSOPUID;
	// get no of Acquisitions done
	IrradiationEventCout = dbReader->GetAcquisitionIDs().size();
	// delete db reader as it is of no longer use
	delete dbReader;
#if 0
	// dump SR contents to log
	LOG_ERROR("=========================== DAC repo details  START ==========================================");
	std::stringstream ss;
	mCRepo->PrintXPath(ss,0);
	LOG_ERROR(ss.str().c_str());
	LOG_ERROR("=========================== DAC repo details  END ============================================");
#endif
}

bool AppCallbackReader::Filldata(
		DcmDom::SRDom::SRCodeCardXPathsType &xpathsToReplicate,
		DcmDom::SRDom::SRCodeCardXPathsType &xpathsToRemove,
		RDSRDBReader *DBReader, std::string examType) 
{
	LOG_DEBUG("AppCallbackReader::Filldata");
	// Check if anonymization needed
	bool anonymize(false);
        AtlString studyId = DBReader->GetValue(RDSRDBReader::Study, 1, 10);
        AtlString patDOB = DBReader->GetValue(RDSRDBReader::Study, 1, 6);
        if((strcmp(studyId.c_str(),"Anon_Study_ID")==0)  && (strcmp(patDOB.c_str(),"19000101")==0)) {
                LOG_INFO("RDSR tags to be anonymized");
		anonymize = true;
        }

	// first Patient Module
	ContentTagsPatientModule ctPatient;
	if (!ctPatient.FillPatientModule(DBReader, mDRepo, mRemoveDRepo))
	{
		// error occured in filling Tags
		LOG_DEBUG("Tags fetch failed for Parient module for " << mStudyUID.c_str());
		return false;
	}

	// Study Module
	ContentTagsStudyModule ctStudy;
	LOG_DEBUG("ExamType in appcallback reader" << examType.c_str());
	if (!ctStudy.FillStudyModule(DBReader, mDRepo, mRemoveDRepo, examType))
	{
		// error occured in filling Tags
		LOG_DEBUG("Tags fetch failed for study module for " << mStudyUID.c_str() << "-ExamType" << examType.c_str());
		return false;
	}

	ContentTagsSeriesModule ctSeries;
	if (!ctSeries.FillSeriesModule(DBReader, mDRepo, mSeriesUID, mCurrentDate, mCurrentTime))
	{
		// error occured in filling Tags
		LOG_DEBUG("Tags fetch failed for series module for " << mStudyUID.c_str());
		return false;
	}

	ContentTagsFoRModule ctFoR;
	if (!ctFoR.FillFoRModule(DBReader, mDRepo, mRemoveDRepo, mRDSRParam, anonymize))
	{
		// error occured in filling Tags
		LOG_DEBUG("Tags fetch failed for Frame of Reference module for " << mStudyUID.c_str());
		return false;
	}

	ContentTagsEquipmentModule ctEquipment;
	if (!ctEquipment.FillEquipmentModule(DBReader, mDRepo, mRemoveDRepo, anonymize))
	{
		// error occured in filling Tags
		LOG_DEBUG("Tags fetch failed for Equipment module for " << mStudyUID.c_str());
		return false;
	}

	ContentTagsDocumentModule ctDocument;
	if (!ctDocument.FillDocumentModule(DBReader, mDRepo, mCurrentDate, mCurrentTime, mInstanceNo,
					   mSOPUID, mRemoveDRepo, mRDSRParam->GetSpecificCharSet()))
	{
		// error occured in filling Tags
		LOG_DEBUG("Tags fetch failed for Document module for " << mStudyUID.c_str());
		return false;
	}

	// fill SR Content

	try
	{

		ContentData content;
		content.StudyID = mStudyUID;
		content.DapConversionfactor = mRDSRParam->GetDAPConversionFactor();
		content.event = mStudyType;
		content.model = mSystemType;
		content.csd = GetCsdType(); //0-SRT, 1- SCT

		DcmDom::SRDom::SRCodeCardXPath mXPath;

		ContentTID10001 srContent(mXPath, 0);
		srContent.FillSRContent(xpathsToReplicate, xpathsToRemove, &content, DBReader, mCRepo);
	}
	catch (std::exception &ex)
	{
		LOG_ERROR("<Exception> SR Content could not be filled : " << ex.what());
		LOG_DEBUG("SR Content fetch failed for " << mStudyUID.c_str());

		return false;
	}
	LOG_INFO("SR Content fetch COMPLETED ");
	return true;
}

bool AppCallbackReader::DetermineSystemType(const RDSRDBReader *DBReader)
{

	mSystemType = NoSystemType;
	mStudyType = NoStudyType;
	try
	{
		LOG_INFO("Determining the system type for the creation of the RDSR");

		if (DBReader->GetAcquisitionIDs().size() == 0)
		{
			// no exposures hencre no need to proceed.
			return true;
		}

		//first determine the System type, i.e. DF or G3
		// this is done based on Model Name
		AtlString value = DBReader->GetValue(RDSRDBReader::Equipment, 1, 7);

		std::string strValue = value.c_str();
		if (strValue.find("Optima XR240") != std::string::npos)
		{
			//Optima XR240
			mSystemType = Mobile;
		}
		else if (strValue.find("AMX Navigate") != std::string::npos)
		{
			//Mobile 2.0
			mSystemType = Mobile;
			// Dragon fly will only contain Cassette
		}
		else
		{
			mSystemType = Fixed;
		}

		for (std::map<std::string, EventType>::const_iterator it = (DBReader->GetAcquisitionIDs()).begin(); it != (DBReader->GetAcquisitionIDs()).end(); ++it)
		{
			if (mStudyType == NoStudyType)
			{
				EventType ev = it->second; // assign the type of first event;
				switch (ev)
				{
				case CASSETTE:
					mStudyType = Cassette;
					break;
				case DIGITALCASSETTE:
					mStudyType = DigitalCassette;
					break;
				case TABLE:
					mStudyType = Table;
					break;
				case WALLSTAND:
					mStudyType = WallStand;
					break;
				default:
					mStudyType = NoStudyType;
				}
			}
			else
			{
				// mStudyType contains study type till previous iteration
				// update StudyType with considearu=ion of this event

				// if previous was same as current , no change will be done,
				// if previous was different than current , study type will update
				// 1. Wall Stand
				// 2. Table
				// 3. Housing [Wall Stand +/ Table ]
				// 4. Digital cassette
				// 5. Digital [Housing + Digital Cassette]
				// 6. Analog Cassette
				// 7. Mixed / Composite [ Analog Cassette + Digital ].

				// if mixed found ( analog casssette along with any digital mode ) ,
				// we can directly exit
				if ((it->second == CASSETTE && mStudyType != Cassette) || (it->second != CASSETTE && mStudyType == Cassette))
				{
					mStudyType = Mixed; // contains both cassette and digital
					return true;	// no need to iterate further
				}
				else if ((it->second == DIGITALCASSETTE && mStudyType != DigitalCassette) || (it->second != DIGITALCASSETTE && mStudyType == DigitalCassette))
				{
					mStudyType = Digital; // contains both cassette and digital
				}
				else if ((it->second == TABLE && mStudyType != Table) || (it->second != TABLE && mStudyType == Table))
				{
					mStudyType = InHousing; // contains both cassette and digital
				}
				else if ((it->second == WALLSTAND && mStudyType != WallStand) || (it->second != WALLSTAND && mStudyType == WallStand))
				{
					mStudyType = InHousing; // contains both cassette and digital
				}
			}
		}
		return true;
	}
	catch (const std::exception &ex)
	{
		LOG_ERROR("<Exception> : " << ex.what());
		return false;
	}
}

bool AppCallbackReader::generateData(std::string SeriesID, std::string SopInstanceID, int OldInstanceNo, const RDSRDBReader *DBReader)
{

	int instanceNo = 1;
	// create Series Id if not present
	if (SeriesID == "")
	{
		AtlString value = RDSRHelper::GetNewUid();
		mSeriesUID = value.c_str();
		LOG_DEBUG("new GUID" << mSeriesUID);

		// default InstanceNo will be new instance No
	}
	else
	{
		mSeriesUID = SeriesID;
		instanceNo = OldInstanceNo + 1;
	}

	mInstanceNo = DMUtility::ToString(instanceNo);

	if (SopInstanceID == "")
	{
		// SR image SOP Instance UID for Integrated
		AtlString valueUID = RDSRHelper::GetNewUid();
		mSOPUID = valueUID.c_str();
		LOG_DEBUG("new SOPInstanceUID for Integrated " << mSOPUID);
	}
	else
	{
		mSOPUID = SopInstanceID;
	}

	mCurrentTime = RDSRHelper::GetCurrentTime();
	mCurrentDate = RDSRHelper::GetCurrentDate();

	return true;
}

void AppCallbackReader::ReadElem(DcmDom::IDcmElement &ptr, DcmDom::DcmDataItemPtr &itemPtr)
{

	DcmElementPtr ret = itemPtr->FindDcmElement(ptr.GetTag());
	if (ret == nullptr)
	{
		LOG_ERROR(" Element not found for " << ptr.GetTag().GetTagName().c_str());
		return;
	}
	ret->GetDcmValue().SetVR(ptr.GetDcmValue().GetVR());
	ret->GetDcmValue().SetVM(ptr.GetDcmValue().GetVM());

	ptr.SetDcmValue(ret->GetDcmValue());
}

void AppCallbackReader::CopyTo(DcmDom::IDcmElement &ptr, DcmElementPtr &ret)
{

	LOG_INFO(" AppCallbackReader::CloneTo");

	DcmSQElementPtr elmPtr = dynamic_cast<DcmSQElementPtr>(&ptr);
	if (elmPtr == NULL) // Coverty check
	{
		LOG_ERROR(" Error: AppCallbackReader::CopyTo  Not a SQ element");
		return;
	}

	DcmSQElementPtr sqElmPtr = dynamic_cast<DcmSQElementPtr>(ret);
	if (sqElmPtr) // Coverty check
	{

		for (int i = 0; i < sqElmPtr->Size(); i++)
		{

			DcmDom::DcmElementPtr TempParntPtr = sqElmPtr->At(i);

			if (TempParntPtr->IsSQElement())
			{
				DcmSQElementPtr SQElementPtr(new DcmDom::DcmSQElement());
				//Coverity fix start for CID 330142
				if (SQElementPtr)
				{
					SQElementPtr->SetDcmTag(TempParntPtr->GetTag());

					SQElementPtr->SetDcmValue(TempParntPtr->GetDcmValue());
					SQElementPtr->GetDcmValue().SetVR(TempParntPtr->GetDcmValue().GetVR());
					SQElementPtr->GetDcmValue().SetVM(TempParntPtr->GetDcmValue().GetVM());

					CopyTo(*SQElementPtr, TempParntPtr);
					elmPtr->AddDcmElement(SQElementPtr);
				}
				else
				{
					LOG_ERROR("SQ elementpointer is null");
				}
				//Coverity fix end for CID 330142
			}
			else
			{
				DcmDom::DcmElementPtr sqdcmElem(new DcmDom::DcmElement());
				sqdcmElem->SetDcmTag(TempParntPtr->GetTag());
				sqdcmElem->SetDcmValue(TempParntPtr->GetDcmValue());
				sqdcmElem->GetDcmValue().SetVR(TempParntPtr->GetDcmValue().GetVR());
				sqdcmElem->GetDcmValue().SetVM(TempParntPtr->GetDcmValue().GetVM());
				elmPtr->AddDcmElement(sqdcmElem);
			}
		}
	}
	else
	{
		LOG_ERROR("Error: AppCallbackReader::CopyTo  sqElmPtr is not a SQ element");
	}
}

void AppCallbackReader::ReadElem(DcmDom::IDcmElement &elm)
{
	LOG_DEBUG("Reader filling elem : " << elm.GetTag().GetTagName().c_str());

	if (elm.IsSQElement())
	{
		LOG_INFO("This IS SQ Element" << elm.GetTag().GetTagName().c_str());
		DcmElementPtr ret = mDRepo->FindDcmElement(elm.GetTag());
		if (ret == nullptr)
		{
			LOG_ERROR(" Element not found for " << elm.GetTag().GetTagName().c_str());
			return;
		}

		ret->GetDcmValue().SetVR(elm.GetDcmValue().GetVR());
		elm.SetDcmValue(ret->GetDcmValue());
		DcmSQElementPtr elmPtr = dynamic_cast<DcmSQElementPtr>(&elm);
		if (elmPtr) // Coverty check
		{
			elmPtr->RemoveAllChildren();
		}

		CopyTo(elm, ret);
	}
	else
	{
		ReadElem(elm, mDRepo);
	}
}

void AppCallbackReader::ReadElem(
    DcmDom::IDcmSQElement::DcmSQXPathType &dcmSqXPath)
{

	LOG_DEBUG("Reading dicom SQ Element SIZE :" << dcmSqXPath.size());

	DcmDataItemPtr sqElmPtr = mDRepo;
	for (size_t i = 0; i < dcmSqXPath.size() && sqElmPtr; ++i)
	{
		DcmElementPtr elemPtr = mDRepo->FindDcmElement(dcmSqXPath.at(i)->GetTag());

		if (dcmSqXPath.at(i)->IsSQElement())
		{
			if (elemPtr == nullptr)
			{
				break;
			}
			else
			{
				sqElmPtr = dynamic_cast<DcmSQElementPtr>(elemPtr);
				//ReadElem(sqElmPtr->)
			}
		}
		else
		{
			LOG_DEBUG("Reading dicom element : " << dcmSqXPath.at(i)->GetTag().GetTagName().c_str());
			ReadElem(*dcmSqXPath.at(i), sqElmPtr);
		}
	}
}

void AppCallbackReader::ReadElem(DcmDom::SRDom::SRContentItem &srContentItem)
{

	try
	{

		SRCodeCardXPath xpath = srContentItem.GetXPath();
		SRNodePtr srNodePtr = mCRepo->GetValueFor(xpath);

		switch (srContentItem.GetVTType())
		{
		case SRContentItem::VT_NONE:
		case SRContentItem::CONTAINER:
		{
			break;
		}
		case SRContentItem::CODE:
		{

			DYNAMIC_DERIVED_CAST(SRConceptCodeItem, srNodePtr, citemPtr);

			//get the derived reference of callback object.
			((SRConceptCodeItem &)(srContentItem)).SetConceptCode(citemPtr->GetConceptCode());

			break;
		}

		case SRContentItem::TEXT:
		{
			//get the derived pointer from lookup object.
			DYNAMIC_DERIVED_CAST(SRTextItem, srNodePtr, citemPtr);

			//get the derived reference of callback object.
			((SRTextItem &)(srContentItem)).SetText(citemPtr->GetText());

			break;
		}
		case SRContentItem::NUM:
		{
			//get the derived pointer from lookup object.
			DYNAMIC_DERIVED_CAST(SRNumItem, srNodePtr, citemPtr);

			DYNAMIC_DERIVED_REF_CAST(SRNumItem, srContentItem, numItem);
			numItem.GetNum().SetFloatValue(citemPtr->GetNum().GetFloatValue());
			numItem.GetNum().SetConceptCode(citemPtr->GetNum().GetConceptCode());
			numItem.GetNum().SetMeasuredValueAbsent(citemPtr->GetNum().IsMeasuredValueAbsent());

			break;
		}
		case SRContentItem::PNAME:
		{
			//get the derived pointer from lookup object.
			DYNAMIC_DERIVED_CAST(SRPNameItem, srNodePtr, citemPtr);

			DYNAMIC_DERIVED_REF_CAST(SRPNameItem, srContentItem, item);
			item.SetPName(citemPtr->GetName());

			break;
		}
		case SRContentItem::DATE:
		{
			//get the derived pointer from lookup object.
			DYNAMIC_DERIVED_CAST(SRDateItem, srNodePtr, citemPtr);

			DYNAMIC_DERIVED_REF_CAST(SRDateItem, srContentItem, item);

			item.SetDate(citemPtr->GetDate());

			break;
		}
		case SRContentItem::TIME:
		{
			//get the derived pointer from lookup object.
			DYNAMIC_DERIVED_CAST(SRTimeItem, srNodePtr, citemPtr);

			DYNAMIC_DERIVED_REF_CAST(SRTimeItem, srContentItem, item);
			item.SetTime(citemPtr->GetTime());

			break;
		}
		case SRContentItem::DATETIME:
		{
			//get the derived pointer from lookup object.
			DYNAMIC_DERIVED_CAST(SRDateTimeItem, srNodePtr, citemPtr);

			DYNAMIC_DERIVED_REF_CAST(SRDateTimeItem, srContentItem, item);
			item.SetDateTime(citemPtr->GetDateTime());

			break;
		}
		case SRContentItem::UIDREF:
		{
			//get the derived pointer from lookup object.
			DYNAMIC_DERIVED_CAST(SRUIDRefItem, srNodePtr, citemPtr);

			DYNAMIC_DERIVED_REF_CAST(SRUIDRefItem, srContentItem, item);

			item.SetValue(citemPtr->GetValue());

			break;
		}
		case SRContentItem::IMAGE:
		{
			//get the derived pointer from lookup object.
			DYNAMIC_DERIVED_CAST(SRImageRefItem, srNodePtr, citemPtr);

			DYNAMIC_DERIVED_REF_CAST(SRImageRefItem, srContentItem, item);
			//Coverity Fix for DM
			if (NULL != citemPtr)
			{
				item.SetValues(*citemPtr);
			}
			//FUNC_NOT_IMPLEMENTED;
			break;
		}
		case SRContentItem::WAVEFORM:
		{
			FUNC_NOT_IMPLEMENTED;
			break;
		}
		case SRContentItem::COMPOSITE:
		{
			LOG_DEBUG("case SRContentItem::COMPOSITE: ");
			FUNC_NOT_IMPLEMENTED;
			break;
		}
		case SRContentItem::SCOORD:
		{
			LOG_DEBUG("SRContentItem::SCOORD:");
			FUNC_NOT_IMPLEMENTED;
			break;
		}
		case SRContentItem::TCOORD:
		{
			LOG_DEBUG("SRContentItem::TCOORD:");
			FUNC_NOT_IMPLEMENTED;
			break;
		}
		default:
			break;
		}
	}
	catch (std::exception &ex)
	{
		LOG_ERROR("<Exception> : " << ex.what());
	}
}
} // namespace DoseAppClient
