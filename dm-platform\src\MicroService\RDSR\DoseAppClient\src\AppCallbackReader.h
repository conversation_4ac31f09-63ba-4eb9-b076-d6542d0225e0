/*
 * AppCallbackReader.h
 *
 *  Created on: May 23, 2017
 *      Author: 212591751
 */
/* Copyright (c) 2017 General Electric Medical Systems, All rights reserved. */
/*****************************************************************************
 FILE
 AppCallbackReader.cpp

 DESCRIPTION
 Reader for SR Dose to be used by RDSR Service

 REV    DATE         BY              SPR & REMARKS
 ----  -----------  ---------------  ------------------------------------------
 1     <USER>   <GROUP>       N/A           Initial Implementation
 2     20-Dec-2017   vishnu          HCSDM00491932 Changes done for getting RDSR Series Instance UID and SOP Instance UID for MPPS N-Set
 3	   03-09-2018	 Sreeja	 	  Private tag to enable/disable <PERSON><PERSON> has set during study recreation.
 4     07-Aug-2019   Prakash B	     HCSDM00563501 Manage RDSR creation in a separate thread
 *****************************************************************************/

#ifndef DOSEAPPCLIENT_AppCallbackReader_H_
#define DOSEAPPCLIENT_AppCallbackReader_H_

#include "IReader.h"
#include "IDcmElement.h"
#include "IDcmSQElement.h"
#include "IDcmDataItem.h"

#include "SRContentRepo.h"
#include "RDSRCreateParam.h"
#include "RDSRDBReader.h"
#include "RDSRHelper.h"

#include <vector>
#include <string>

class InMemStream;

namespace DoseAppClient {

class AppCallbackReader: public DcmDom::IReader {
public:
	AppCallbackReader();
	virtual ~AppCallbackReader();

	void SetDicomElement(DcmDom::DcmElementPtr& obj) {
		mDRepo->AddDcmElement(obj);
	}

	//get configured method
	void SetCsdType(const std::string& csdStr) {
		if(csdStr == "SRT")
			csdType = DcmDom::CsdType::SRT;
		else if (csdStr == "SCT")
			csdType = DcmDom::CsdType::SCT;
	}

	virtual DcmDom::CsdType GetCsdType()
	{
		return csdType;
	}

	virtual void ReadElem(DcmDom::IDcmElement& ptr);
	virtual void ReadElem(DcmDom::IDcmSQElement::DcmSQXPathType &dcmSqXPath);
	virtual void ReadElem(DcmDom::SRDom::SRContentItem &srContentItem);
	
	virtual void PopulateDataFor(InMemStream& memStream, DcmDom::SRDom::SRCodeCardXPathsType & xpathsToReplicate,
				int &IrradiationEventCout,std::string &SeriesInstUID, std::string &SOPInstUID,
				DcmDom::SRDom::SRCodeCardXPathsType & xpathsToRemove);
	
	//virtual DcmDom::SRDom::SRCodeCardXPathsType* GetDICOMElemToRemove () { return mRemoveCRepoPaths; };

	virtual DcmDom::IDcmDataItem* GetDICOMElemToRemove() { return mRemoveDRepo; };


protected:
	DcmDom::IDcmDataItem* mDRepo;
	DcmDom::IDcmDataItem* mRemoveDRepo;
	DcmDom::SRDom::SRContentRepo *mCRepo;

	void ReadElem(DcmDom::IDcmElement& ptr,DcmDom::DcmDataItemPtr& itemPtr );

private:

	std::string mStudyUID;
	
	std::string mSeriesUID;
	std::string mCurrentTime;
	std::string mCurrentDate;

	std::string mInstanceNo;
	std::string mSOPUID;

	StudyType mStudyType;
	SystemType mSystemType;
	DcmDom::CsdType csdType;

	RDSRCreateParam * mRDSRParam;
	
	bool generateData(std::string SeriesID, std::string SopInstanceID, int OldInstanceNo, const RDSRDBReader* DBReader);
	bool DetermineSystemType( const RDSRDBReader* DBReader);
	bool Filldata(DcmDom::SRDom::SRCodeCardXPathsType& xpathsToReplicate,
			DcmDom::SRDom::SRCodeCardXPathsType& xpathsToRemove, RDSRDBReader *DBReader, std::string examType);
	void CopyTo(DcmDom::IDcmElement& ptr, DcmDom::DcmElementPtr& ret);
};

} /* namespace Wrapper */
#endif /* DOSEAPPCLIENT_AppCallbackReader_H_ */
