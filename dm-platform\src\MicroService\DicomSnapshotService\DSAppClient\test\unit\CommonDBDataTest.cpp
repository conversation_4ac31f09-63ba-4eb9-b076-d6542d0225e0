// CommonDBDataTest.cpp
// Unit tests for DSAppClient::CommonDBData
// This file is auto-generated to maximize function and condition/decision coverage.
// Author: Aryan

#include "gtest/gtest.h"
#include "gmock/gmock.h"
#include "CommonDBData.h"

using namespace DSAppClient;
using ::testing::Return;
using ::testing::_;

// Mock class for AtlSQLResultSet
class MockAtlSQLResultSet : public AtlSQLResultSet {
    // No need for MOCK_METHOD or any method, just a dummy class for pointer testing
};

class CommonDBDataTest : public ::testing::Test {
protected:
    CommonDBData* dbData;
    void SetUp() override {
        dbData = new CommonDBData();
    }
    void TearDown() override {
        delete dbData;
    }
};

// Test constructor and default state
TEST_F(CommonDBDataTest, DefaultConstructorInitializesMembers) {
    EXPECT_EQ(dbData->getStudyModule(), nullptr);
    EXPECT_EQ(dbData->getSeriesModule(), nullptr);
    EXPECT_EQ(dbData->getEquipmentModule(), nullptr);
    EXPECT_EQ(dbData->getPatientExtentedModule(), nullptr);
    EXPECT_EQ(dbData->getAcquisitionModule(), nullptr);
    EXPECT_EQ(dbData->getImageModule(), nullptr);
    EXPECT_FALSE(dbData->getPatientExtentedModuleAvailable());
}

// Test set/get for StudyModule
TEST_F(CommonDBDataTest, SetAndGetStudyModule) {
    auto* mock = new MockAtlSQLResultSet();
    dbData->setStudyModule(mock);
    EXPECT_EQ(dbData->getStudyModule(), mock);
    // Ownership test: destructor should delete
    dbData->setStudyModule(nullptr);
}

// Test set/get for SeriesModule
TEST_F(CommonDBDataTest, SetAndGetSeriesModule) {
    auto* mock = new MockAtlSQLResultSet();
    dbData->setSeriesModule(mock);
    EXPECT_EQ(dbData->getSeriesModule(), mock);
    dbData->setSeriesModule(nullptr);
}

// Test set/get for EquipmentModule
TEST_F(CommonDBDataTest, SetAndGetEquipmentModule) {
    auto* mock = new MockAtlSQLResultSet();
    dbData->setEquipmentModule(mock);
    EXPECT_EQ(dbData->getEquipmentModule(), mock);
    dbData->setEquipmentModule(nullptr);
}

// Test set/get for PatientExtentedModule
TEST_F(CommonDBDataTest, SetAndGetPatientExtentedModule) {
    auto* mock = new MockAtlSQLResultSet();
    dbData->setPatientExtentedModule(mock);
    EXPECT_EQ(dbData->getPatientExtentedModule(), mock);
    dbData->setPatientExtentedModule(nullptr);
}

// Test set/get for AcquisitionModule
TEST_F(CommonDBDataTest, SetAndGetAcquisitionModule) {
    auto* mock = new MockAtlSQLResultSet();
    dbData->setAcquisitionModule(mock);
    EXPECT_EQ(dbData->getAcquisitionModule(), mock);
    dbData->setAcquisitionModule(nullptr);
}

// Test set/get for ImageModule
TEST_F(CommonDBDataTest, SetAndGetImageModule) {
    auto* mock = new MockAtlSQLResultSet();
    dbData->setImageModule(mock);
    EXPECT_EQ(dbData->getImageModule(), mock);
    dbData->setImageModule(nullptr);
}

// Test set/get for PatientExtentedModuleAvailable
TEST_F(CommonDBDataTest, SetAndGetPatientExtentedModuleAvailable) {
    dbData->setPatientExtentedModuleAvailable(true);
    EXPECT_TRUE(dbData->getPatientExtentedModuleAvailable());
    dbData->setPatientExtentedModuleAvailable(false);
    EXPECT_FALSE(dbData->getPatientExtentedModuleAvailable());
}

// Test destructor deletes all modules
TEST(CommonDBDataDestructorTest, DestructorDeletesModules) {
    auto* dbData = new CommonDBData();
    auto* study = new MockAtlSQLResultSet();
    auto* series = new MockAtlSQLResultSet();
    auto* equipment = new MockAtlSQLResultSet();
    auto* patient = new MockAtlSQLResultSet();
    auto* acquisition = new MockAtlSQLResultSet();
    auto* image = new MockAtlSQLResultSet();
    dbData->setStudyModule(study);
    dbData->setSeriesModule(series);
    dbData->setEquipmentModule(equipment);
    dbData->setPatientExtentedModule(patient);
    dbData->setAcquisitionModule(acquisition);
    dbData->setImageModule(image);
    // No explicit delete, destructor should clean up
    delete dbData;
    SUCCEED();
}

// Test edge: double delete safety
TEST(CommonDBDataDestructorTest, DoubleDeleteSafety) {
    auto* dbData = new CommonDBData();
    auto* study = new MockAtlSQLResultSet();
    dbData->setStudyModule(study);
    dbData->setStudyModule(nullptr); // Should not double delete
    delete dbData;
    SUCCEED();
}

// Test all combinations of set/get for null and non-null
TEST_F(CommonDBDataTest, SetNullModules) {
    dbData->setStudyModule(nullptr);
    dbData->setSeriesModule(nullptr);
    dbData->setEquipmentModule(nullptr);
    dbData->setPatientExtentedModule(nullptr);
    dbData->setAcquisitionModule(nullptr);
    dbData->setImageModule(nullptr);
    EXPECT_EQ(dbData->getStudyModule(), nullptr);
    EXPECT_EQ(dbData->getSeriesModule(), nullptr);
    EXPECT_EQ(dbData->getEquipmentModule(), nullptr);
    EXPECT_EQ(dbData->getPatientExtentedModule(), nullptr);
    EXPECT_EQ(dbData->getAcquisitionModule(), nullptr);
    EXPECT_EQ(dbData->getImageModule(), nullptr);
}

// Test repeated set/delete for memory leak/robustness
TEST(CommonDBDataDestructorTest, RepeatedSetDelete) {
    auto* dbData = new CommonDBData();
    for (int i = 0; i < 10; ++i) {
        dbData->setStudyModule(new MockAtlSQLResultSet());
        dbData->setSeriesModule(new MockAtlSQLResultSet());
        dbData->setEquipmentModule(new MockAtlSQLResultSet());
        dbData->setPatientExtentedModule(new MockAtlSQLResultSet());
        dbData->setAcquisitionModule(new MockAtlSQLResultSet());
        dbData->setImageModule(new MockAtlSQLResultSet());
        dbData->setStudyModule(nullptr);
        dbData->setSeriesModule(nullptr);
        dbData->setEquipmentModule(nullptr);
        dbData->setPatientExtentedModule(nullptr);
        dbData->setAcquisitionModule(nullptr);
        dbData->setImageModule(nullptr);
    }
    delete dbData;
    SUCCEED();
}

// Test set/get with mixed null/non-null
TEST_F(CommonDBDataTest, MixedNullNonNullModules) {
    auto* study = new MockAtlSQLResultSet();
    dbData->setStudyModule(study);
    dbData->setSeriesModule(nullptr);
    auto* equipment = new MockAtlSQLResultSet();
    dbData->setEquipmentModule(equipment);
    dbData->setPatientExtentedModule(nullptr);
    auto* acquisition = new MockAtlSQLResultSet();
    dbData->setAcquisitionModule(acquisition);
    dbData->setImageModule(nullptr);
    EXPECT_EQ(dbData->getStudyModule(), study);
    EXPECT_EQ(dbData->getSeriesModule(), nullptr);
    EXPECT_EQ(dbData->getEquipmentModule(), equipment);
    EXPECT_EQ(dbData->getPatientExtentedModule(), nullptr);
    EXPECT_EQ(dbData->getAcquisitionModule(), acquisition);
    EXPECT_EQ(dbData->getImageModule(), nullptr);
    dbData->setStudyModule(nullptr);
    dbData->setEquipmentModule(nullptr);
    dbData->setAcquisitionModule(nullptr);
}


// Test set/get for PatientExtentedModuleAvailable with all bool values
TEST_F(CommonDBDataTest, PatientExtentedModuleAvailableAllValues) {
    for (int i = 0; i < 2; ++i) {
        dbData->setPatientExtentedModuleAvailable(i != 0);
        EXPECT_EQ(dbData->getPatientExtentedModuleAvailable(), i != 0);
    }
}
